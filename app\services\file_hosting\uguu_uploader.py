# app/services/file_hosting/uguu_uploader.py
import asyncio
import logging
import tempfile
import os
import aiohttp
from typing import Optional
from aiogram import Bot

# Убедись, что импортируешь протокол
from .base_uploader import FileUploader

logger = logging.getLogger(__name__)

class UguuUploader(FileUploader):
    """
    Реализация загрузчика файлов на хостинг uguu.se.
    """
    UPLOAD_URL = "https://uguu.se/upload"

    async def upload_by_file_id(self, bot: Bot, file_id: str) -> Optional[str]:
        """
        Скачивает файл из Telegram по file_id, загружает на uguu.se и возвращает URL.
        """
        # Используем временный файл, чтобы не засорять диск
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file_path = temp_file.name

        try:
            # 1. Скачиваем файл с серверов Telegram
            file_info = await bot.get_file(file_id)
            await bot.download_file(file_info.file_path, destination=temp_file_path)
            logger.info(f"Файл {file_id} успешно скачан во временный файл: {temp_file_path}")

            # 2. Загружаем файл на хостинг
            async with aiohttp.ClientSession() as session:
                with open(temp_file_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('files[]', f, filename='upload.tmp')

                    logger.info(f"Отправка файла на {self.UPLOAD_URL}...")
                    async with session.post(self.UPLOAD_URL, data=data) as response:
                        response.raise_for_status() # Проверка на HTTP ошибки (4xx/5xx)

                        response_data = await response.json()
                        logger.debug(f"Ответ от uguu.se: {response_data}")

                        # 3. Извлекаем URL из ответа
                        file_url = response_data.get('files', [{}])[0].get('url')
                        if not file_url:
                            raise ValueError("URL не найден в ответе от хостинга.")

                        # Заменяем экранированные слэши, если они есть
                        file_url = file_url.replace('\\/', '/')
                        logger.info(f"Файл успешно загружен. URL: {file_url}")
                        return file_url

        except Exception as e:
            logger.error(f"Ошибка при загрузке файла {file_id} на хостинг: {e}", exc_info=True)
            return None
        finally:
            # 4. Гарантированно удаляем временный файл
            if os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    logger.debug(f"Временный файл {temp_file_path} удален.")
                except PermissionError:
                    # На Windows иногда файл может быть заблокирован, попробуем позже
                    logger.warning(f"Не удалось удалить временный файл {temp_file_path} сразу, попробуем позже")
                    try:
                        await asyncio.sleep(0.1)  # Небольшая задержка
                        os.unlink(temp_file_path)
                        logger.debug(f"Временный файл {temp_file_path} удален после задержки.")
                    except Exception as cleanup_error:
                        logger.error(f"Не удалось удалить временный файл {temp_file_path}: {cleanup_error}")
                except Exception as cleanup_error:
                    logger.error(f"Ошибка при удалении временного файла {temp_file_path}: {cleanup_error}")