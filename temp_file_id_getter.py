# temp_file_id_getter.py - Временный скрипт для получения file_id
import asyncio
import os
from dotenv import load_dotenv
from aiogram import Bo<PERSON>, Dispatcher, Router, F
from aiogram.types import Message

# Загружаем переменные окружения
load_dotenv()

# Создаем роутер
router = Router()

@router.message(F.photo)
async def get_file_id(message: Message):
    """Простой хендлер для получения file_id изображения"""
    photo = message.photo[-1]  # Берем фото максимального качества
    file_id = photo.file_id
    
    # Выводим file_id в консоль и отправляем пользователю
    print(f"🔍 FILE_ID: {file_id}")
    await message.answer(f"📋 FILE_ID этого изображения:\n`{file_id}`", parse_mode="Markdown")

async def main():
    """Запуск временного бота для получения file_id"""
    bot_token = os.getenv("TG_TOKEN")
    if not bot_token:
        print("❌ Не найден токен бота в TG_TOKEN")
        return
    
    bot = Bot(token=bot_token)
    dp = Dispatcher()
    dp.include_router(router)
    
    print("🤖 Временный бот запущен! Отправьте ему любое изображение для получения file_id")
    print("⏹️ Для остановки нажмите Ctrl+C")
    
    try:
        await dp.start_polling(bot)
    except KeyboardInterrupt:
        print("\n🛑 Бот остановлен")
    finally:
        await bot.session.close()

if __name__ == "__main__":
    asyncio.run(main())
