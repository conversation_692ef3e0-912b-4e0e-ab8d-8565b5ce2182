#!/usr/bin/env python3
"""
Тест для проверки функции set_active_image_file_id.
Этот тест проверяет, что новая функция корректно сохраняет file_id активного изображения.
"""

import asyncio
import sys
import os

# Добавляем корневую директорию проекта в путь
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.supabase.users import set_active_image_file_id, get_user_by_tg_id


async def test_set_active_image_file_id():
    """
    Тестирует функцию set_active_image_file_id.
    """
    print("🧪 Тестирование функции set_active_image_file_id...")
    
    # Тестовые данные
    test_tg_id = 7706349617 # Замените на реальный tg_id из вашей базы
    test_file_id = "BAADBAADrwADBREAAYag2ycWmJmdAg"  # Тестовый file_id
    
    try:
        # Проверяем, что пользователь существует
        user = await get_user_by_tg_id(test_tg_id)
        if not user:
            print(f"❌ Пользователь с tg_id {test_tg_id} не найден в базе данных")
            print("💡 Создайте пользователя или измените test_tg_id на существующий")
            return False
        
        print(f"✅ Пользователь найден: {user.get('username', 'N/A')}")
        
        # Сохраняем старое значение для восстановления
        old_file_id = user.get('active_image_file_id')
        print(f"📝 Старое значение active_image_file_id: {old_file_id}")
        
        # Тестируем установку нового file_id
        await set_active_image_file_id(test_tg_id, test_file_id)
        print(f"✅ Функция set_active_image_file_id выполнена успешно")
        
        # Проверяем, что значение действительно изменилось
        updated_user = await get_user_by_tg_id(test_tg_id)
        new_file_id = updated_user.get('active_image_file_id')
        
        if new_file_id == test_file_id:
            print(f"✅ Тест пройден! active_image_file_id обновлен: {new_file_id}")
            
            # Восстанавливаем старое значение (если было)
            if old_file_id:
                await set_active_image_file_id(test_tg_id, old_file_id)
                print(f"🔄 Восстановлено старое значение: {old_file_id}")
            
            return True
        else:
            print(f"❌ Тест не пройден! Ожидалось: {test_file_id}, получено: {new_file_id}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        return False


async def main():
    """
    Главная функция для запуска тестов.
    """
    print("🚀 Запуск тестов для функции активного контекста изображений\n")
    
    success = await test_set_active_image_file_id()
    
    print("\n" + "="*50)
    if success:
        print("🎉 Все тесты пройдены успешно!")
        print("✅ Функция set_active_image_file_id работает корректно")
    else:
        print("❌ Тесты не пройдены")
        print("🔧 Проверьте подключение к базе данных и существование тестового пользователя")


if __name__ == "__main__":
    asyncio.run(main())
