-- =============================================
-- МИГРАЦИЯ: ДОБАВЛЕНИЕ ПОЛЯ active_image_file_id
-- =============================================
-- Этот скрипт добавляет поле active_image_file_id в таблицу users для хранения
-- file_id последнего активного изображения пользователя для контекста редактирования.
--
-- Дата создания: 2025-01-28
-- Версия: 1.0
--
-- Инструкция по использованию:
-- 1. Войдите в панель управления Supabase (https://app.supabase.io)
-- 2. Выберите ваш проект
-- 3. Перейдите в раздел "SQL Editor"
-- 4. Создайте новый запрос, вставьте содержимое этого файла
-- 5. Выполните запрос
--
-- ВАЖНО: Этот скрипт безопасен для выполнения на существующей базе данных.
-- Он добавляет новое поле без изменения существующих данных.

-- Установка часового пояса для текущей сессии
SET timezone TO 'Europe/Moscow';

-- =============================================
-- ЧАСТЬ 1: ДОБАВЛЕНИЕ НОВОГО ПОЛЯ
-- =============================================

-- Добавляем новое поле в таблицу users
ALTER TABLE public.users
ADD COLUMN active_image_file_id TEXT;

-- =============================================
-- ЧАСТЬ 2: ДОБАВЛЕНИЕ КОММЕНТАРИЯ К ПОЛЮ
-- =============================================

-- Устанавливаем комментарий для нового поля, чтобы было понятно его назначение
COMMENT ON COLUMN public.users.active_image_file_id IS 'Хранит file_id последнего изображения (отправленного пользователем или сгенерированного ботом) для контекста редактирования.';

-- =============================================
-- ЧАСТЬ 3: ПРОВЕРКА УСПЕШНОСТИ МИГРАЦИИ
-- =============================================

-- Проверяем, что поле было успешно добавлено
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'active_image_file_id'
    ) THEN
        RAISE NOTICE 'Миграция успешно выполнена: поле active_image_file_id добавлено в таблицу users';
    ELSE
        RAISE EXCEPTION 'Ошибка миграции: поле active_image_file_id не было добавлено';
    END IF;
END $$;

-- =============================================
-- ПРИМЕЧАНИЯ
-- =============================================
-- 1. Поле active_image_file_id добавлено с типом TEXT и значением по умолчанию NULL
-- 2. Все существующие пользователи получат значение NULL для этого поля
-- 3. Новые пользователи также будут создаваться с NULL в этом поле
-- 4. Поле будет обновляться при отправке/генерации изображений для контекста редактирования
-- 5. Триггер update_updated_at_column автоматически применится к новому полю
-- 6. Политики RLS (Row Level Security) автоматически распространятся на новое поле
-- 7. Поле совместимо с существующей архитектурой и не требует изменений в коде
-- =============================================
