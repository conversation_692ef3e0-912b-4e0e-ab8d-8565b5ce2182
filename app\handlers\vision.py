import logging
import os
import base64
import tempfile
import aiofiles
import asyncio
from typing import List
from aiogram import Router, <PERSON>, <PERSON><PERSON>, Dispatcher
from aiogram.types import Message
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from aiogram.enums import ParseMode
from app.localization import get_text
from app.text_messages import llm_response
from app.supabase.messages import save_message
from app.supabase.users import get_user_model, get_user_language
from app.subscription_manager import SubscriptionManager
from config.models_config import MODELS_CONFIG
from app.vision_processing import process_vision_content
from app.utils.request_limiter import check_limits
from app.utils.error_handler import handle_error
from app.utils.formatting import convert_markdown_to_html

router = Router()
logger = logging.getLogger(__name__)

# Множество для хранения ID пользователей, обрабатывающих изображение (vision)
PROCESSING_VISION_USERS = set()

class MediaGroupState(StatesGroup):
    waiting = State()

@router.message(F.photo)
async def handle_photo(message: Message, state: FSMContext, dispatcher: Dispatcher):
    # Проверяем, является ли сообщение частью медиа-группы
    if message.media_group_id:
        # Получаем текущее состояние для этой медиа-группы
        current_state = await state.get_state()
        state_data = await state.get_data()

        # Проверяем, обрабатывали ли мы уже эту медиа-группу
        if (current_state != "MediaGroupState:waiting" or
            state_data.get('media_group_id') != message.media_group_id):
            # Устанавливаем состояние и сохраняем ID медиа-группы
            await state.set_state(MediaGroupState.waiting)
            await state.update_data(media_group_id=message.media_group_id)
            # Отправляем предупреждение только один раз
            user_language = await get_user_language(message.from_user.id)
            single_image_message = get_text("vision", "single_image", user_language)
            await message.answer(single_image_message)
        return

    user_id = message.from_user.id

    # Сбрасываем состояние, так как это одиночное изображение
    await state.clear()

    # Проверка модели на поддержку vision
    user_model = await get_user_model(user_id)
    if not MODELS_CONFIG[user_model].get('vision', False):
        user_language = await get_user_language(message.from_user.id)
        model_error_message = get_text("vision", "model_not_supported", user_language)
        await message.answer(model_error_message)
        return

    # Проверяем лимиты и получаем пользователя
    can_proceed, user = await check_limits(message, "text")
    if not can_proceed:
        return

    # Проверяем, не обрабатывает ли пользователь уже изображение
    if user_id in PROCESSING_VISION_USERS:
        # Отправляем сообщение пользователю
        user_language = await get_user_language(user_id)
        already_processing_message = get_text("vision", "already_processing", user_language)
        await message.answer(already_processing_message)
        return

    supabase_user_id = user["id"]

    # Отправка уведомления о начале обработки
    user_language = await get_user_language(message.from_user.id)
    processing_message = get_text("vision", "processing", user_language)

    # Добавляем пользователя в множество перед началом обработки
    PROCESSING_VISION_USERS.add(user_id)
    processing_msg = await message.answer(processing_message)

    temp_file = None
    try:
        # Получение фото максимального качества
        photo = message.photo[-1]
        file = await message.bot.get_file(photo.file_id)

        # ВРЕМЕННО: Выводим file_id для тестирования загрузчика
        print(f"🔍 FILE_ID для тестирования: {photo.file_id}")
        logger.info(f"FILE_ID для тестирования: {photo.file_id}")

        # Создаем временный файл
        temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
        await message.bot.download_file(file.file_path, temp_file.name)
        temp_file.close()  # Закрываем файл после записи

        # Читаем и кодируем в base64
        async with aiofiles.open(temp_file.name, "rb") as image_file:
            image_data = await image_file.read()
            base64_image = base64.b64encode(image_data).decode('utf-8')

        # Используем ID пользователя, полученный ранее

        # Сохранение в базу данных
        await save_message(
            "user",
            "[Изображение]",
            supabase_user_id,
            base64_image,
            message.caption
        )

        # Обработка изображения
        # Получаем mcp_manager из dispatcher
        mcp_manager_instance = dispatcher.get('mcp_manager')

        # Функция уведомления об использовании инструментов
        async def notify_tool_usage(tool_names: List[str]):
            user_language = await get_user_language(message.from_user.id)
            tool_usage_message = get_text("text_processing", "using_mcp_tools", user_language)
            if processing_msg: await processing_msg.edit_text(tool_usage_message)

        response = await llm_response(
            message.caption,
            user_id,
            mcp_manager_instance,
            bot=message.bot,
            message=message,
            tool_usage_callback=notify_tool_usage
        )

        if isinstance(response, dict) and "error" in response:  # Если получили сообщение об ошибке
            if processing_msg: await processing_msg.edit_text(response["error"])
            return

        # Получаем основной текст ответа
        response_content = response.choices[0].message.content

        # Конвертируем Markdown в HTML
        html_response_content = convert_markdown_to_html(response_content)

        # Сохранение ответа ассистента (сохраняем оригинальный текст)
        await save_message(
            "assistant",
            response_content,
            supabase_user_id
        )

        # Отправка ответа пользователю с HTML форматированием
        try:
            # Отправляем конвертированный HTML ответ
            if processing_msg: await processing_msg.edit_text(html_response_content, parse_mode=ParseMode.HTML)
        except Exception as e:
            # Логируем и отправляем fallback
            logger.error(f"Ошибка отправки HTML сообщения (Vision): {e}")
            try:
                # Fallback: отправляем оригинальный текст без форматирования
                if processing_msg: await processing_msg.edit_text("Не удалось отформатировать ответ. Отправляю как есть:\n\n" + response_content, parse_mode=None)
            except Exception as e2:
                logger.error(f"Ошибка отправки простого текста (Vision): {e2}")
                if processing_msg: await processing_msg.edit_text("Произошла ошибка при отправке ответа.")

        # Увеличиваем счетчик запросов
        await SubscriptionManager.increment_request_counter(user_id, "text")

    except Exception as e:
        await handle_error(e, message, "vision.error")
    finally:
        # Удаляем пользователя из множества после завершения (или ошибки)
        PROCESSING_VISION_USERS.discard(user_id) # Используем discard для безопасного удаления

        # Удаляем временный файл
        if temp_file and os.path.exists(temp_file.name):
            try:
                await asyncio.to_thread(os.unlink, temp_file.name)
            except Exception as e:
                # Просто логируем ошибку без отправки сообщения пользователю
                logger.error(f"Ошибка при удалении временного файла: {e}")

