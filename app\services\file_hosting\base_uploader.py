# app/services/file_hosting/base_uploader.py
from typing import Protocol, Optional
from aiogram import Bot

class FileUploader(Protocol):
    """
    Протокол (интерфейс) для всех сервисов загрузки файлов.
    Определяет контракт, которому должны следовать все реализации.
    """

    async def upload_by_file_id(self, bot: Bot, file_id: str) -> Optional[str]:
        """
        Скачивает файл из Telegram по file_id, загружает его на хостинг
        и возвращает прямую публичную ссылку.

        Args:
            bot: Экземпляр aiogram.Bot для скачивания файла.
            file_id: file_id файла в Telegram.

        Returns:
            URL загруженного файла или None в случае ошибки.
        """
        ...