# test_uploader.py
import asyncio
import os
import logging
from dotenv import load_dotenv
from aiogram import Bot

# Указываем путь к нашему новому модулю
from app.services.file_hosting.uguu_uploader import UguuUploader

# Настройка логирования для вывода в консоль
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def main():
    """
    Тестовый скрипт для проверки загрузчика файлов.
    """
    load_dotenv()

    bot_token = os.getenv("TG_TOKEN")
    if not bot_token:
        logging.error("Не найден токен бота в TG_TOKEN. Проверьте ваш .env файл.")
        return

    # СЮДА НУЖНО ВСТАВИТЬ РЕАЛЬНЫЙ FILE_ID ИЗОБРАЖЕНИЯ ИЗ ВАШЕГО БОТА
    # Как получить file_id: просто отправьте боту любое изображение,
    # он ответит его file_id (нужно временно добавить `print(message.photo[-1].file_id)` в хендлер).
    test_file_id = "AgACAgIAAxkBAAJOvGiGkoIyfTlHgMrUnT-k0X73ipLmAAK-ATIbg7EwSEByD0737rlhAQADAgADeQADNgQ"

    if test_file_id == "YOUR_IMAGE_FILE_ID_HERE":
        logging.warning("Пожалуйста, замените 'YOUR_IMAGE_FILE_ID_HERE' на реальный file_id изображения в файле test_uploader.py")
        return

    bot = Bot(token=bot_token)
    uploader = UguuUploader()

    logging.info(f"Тестируем загрузку файла с file_id: {test_file_id}")

    # Запускаем загрузку
    public_url = await uploader.upload_by_file_id(bot, test_file_id)

    if public_url:
        logging.info(f"✅ ТЕСТ УСПЕШЕН! Получен публичный URL: {public_url}")
    else:
        logging.error("❌ ТЕСТ ПРОВАЛЕН. Не удалось получить URL.")

    # Важно корректно закрыть сессию бота
    await bot.session.close()


if __name__ == "__main__":
    asyncio.run(main())